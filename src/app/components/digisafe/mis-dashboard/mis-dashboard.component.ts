import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { OffcanvasComponent } from '../../../shared/offcanvas/offcanvas.component';
import { DigisafeService } from '../../../services/digisafe/digisafe.service';
import { PlantManagementService } from '../../../services/plant-management/plant-management.service';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { ClusterService } from '../../../services/master-management/cluster/cluster.service';
import { OpcoService } from '../../../services/master-management/opco/opco.service';
import { PlantTypeService } from '../../../services/master-management/plant-type/plant-type.service';
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';
import { ToastMessageComponent } from '../../../shared/toast-message/toast-message.component';
import * as XLSX from 'xlsx';
import { NgSelectModule } from '@ng-select/ng-select';

interface Plant {
  id: number;
  name: string;
  clusterId?: number;
  opcoId?: number;
  plantTypeId?: number;
}

interface Cluster {
  id: number;
  title: string;
}

interface Company {
  id: number;
  title: string;
}

interface PlantType {
  id: number;
  title: string;
}

interface MisFilter {
  year: number | null;
  plantId: number[] | null;
  clusterId: number | null;
  opcoId: number | null; // Assuming 'Company' maps to OpCo
  plantTypeId: number | null;
}

interface MisMonthlyData {
  month: number;
  // Using the actual API field names
  AfpCurrent: string;
  AfpBest: string;
  ManHoursWorked: string;
  KmCovered: string;
  OI: string;
  FAC: string;
  MTC: string;
  RLTI: string;
  Fatal: string;
  DangerOccurence: string;
  FireIncident: string;
  VRA: string; // Vehicle related Accident
  propertyDamage: string;
  LSI?: string; // Leak-Spill Incident with API field name LSI
  AccidentCost: string;
  MDL: string;
  NearMissIncident: string;
  SafetyInduction: string;
  TBTBatches: string;
  TBTno: string;
  STP_Plan: string;
  STP_MTD: string;
  ET_Plan: string;
  ET_MTD: string;
  TrainingManHours: string;
  SCMPlan: string;
  SCM_MTD: string;
  PWAPlan: string;
  PWA_MTD: string;
  SA_Plan?: string; // Optional as it might be missing in API
  SA_MTD: string;
  SI_Plan: string;
  SI_MTD: string;
  SafetyItemInspPlan?: string; // Optional as it might be missing in API
  SafetyItemInspMTD?: string; // Optional as it might be missing in API
  SPC_Plan: string;
  SPC_MTD: string;
  EMD_Plan: string;
  EMD_MTD: string;
  IHM_Plan: string;
  IHM_MTD: string;
  SnRaised: string;
  SnClosed: string;
  SnOpen: string;
  SnOpen90days: string;
  SafetyWalkthrough?: string; // Safety Walkthrough by Senior leadership Team
  // Keep LeakSpillIncident for backward compatibility
  LeakSpillIncident?: string;
}


@Component({
  selector: 'app-mis-dashboard',
  standalone: true,
  imports: [CommonModule, FormsModule, OffcanvasComponent, NgbDropdownModule, ToastMessageComponent, NgSelectModule],
  templateUrl: './mis-dashboard.component.html',
  styleUrl: './mis-dashboard.component.scss'
})
export class MisDashboardComponent implements OnInit {
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;

  misData: MisMonthlyData[] = [];
  transformedData: any = {}; // Will hold metrics as rows with month values
  months: number[] = []; // Will hold unique months
  isDownloadingExcel = false;
  downloadType: 'current' | 'all' | null = null;
  metricLabels: { [key: string]: string } = {
    // Using the actual API field names
    'AfpCurrent': 'Accident Free Period (Current)',
    'AfpBest': 'Accident Free Period (Best)',
    'ManHoursWorked': 'Man Hours Worked',
    'KmCovered': 'Kilometres Covered',
    'OI': 'Occupational Illness (OI)',
    'FAC': 'First Aid Case (FAC)',
    'MTC': 'Medical Treatment Case (MTC)',
    'RLTI': 'Reportable Lost Time Injury (R-LTI)',
    'Fatal': 'Fatal',
    'DangerOccurence': 'Dangerous Occurrence (DO)',
    'FireIncident': 'Fire Incident',
    'VRA': 'Vehicle related Accident',
    'propertyDamage': 'Property Damage Incident Nos. (Except Vehicle & Fire Incidents)',
    'LSI': 'Leak-Spill Incident', // Added back with API field name LSI
    'AccidentCost': 'Accident Cost INR (Divided by 1000)',
    'MDL': 'Man Days Lost (MDL)',
    'NearMissIncident': 'Near miss Incident (including High Potential Near Miss Case)',
    'SafetyInduction': 'Safety Induction (Participants)',
    'TBTBatches': 'Tool Box Talk (No of batches.)',
    'TBTno': 'TBT- Participant Nos.',
    'STP_Plan': 'Safety Training Program Plan (Batches)',
    'STP_MTD': 'Safety Training Program - MTD (Batches)',
    'ET_Plan': 'Employee Trained - Plan (Nos)',
    'ET_MTD': 'Employee Trained - MTD (Nos)',
    'TrainingManHours': 'Training Man-Hours (MTD)',
    'SCMPlan': 'Safety Committee Meetings (Plan)', // Corrected mapping
    'SCM_MTD': 'Safety Committee Meetings (MTD)',
    'PWAPlan': 'Permit To Work Audit (Plan)',
    'PWA_MTD': 'Permit To Work Audit (MTD)',
    'SA_Plan': 'Self Assessment (Plan)', // Keep this mapping for backward compatibility
    'SA_MTD': 'Self Assessment (MTD)',
    'SI_Plan': 'Safety-Item / Equipment Inspection (Plan)', // Mapped to Safety-Item / Equipment Inspection (Plan) as requested
    'SI_MTD': 'Safety-Item / Equipment Inspection (MTD)', // Mapped to Safety-Item / Equipment Inspection (MTD) as requested
    'SafetyItemInspPlan': 'Safety-Item / Equipment Inspection (Plan)', // Keep this mapping for backward compatibility
    'SafetyItemInspMTD': 'Safety-Item / Equipment Inspection (MTD)', // Keep this mapping for backward compatibility
    'SPC_Plan': 'Safety Promotional Campaign (Plan)',
    'SPC_MTD': 'Safety Promotional Campaign (MTD)',
    'EMD_Plan': 'Emergency Mock Drill (Plan)',
    'EMD_MTD': 'Emergency Mock Drill (MTD)',
    'IHM_Plan': 'Industrial Hygiene Monitoring (Plan)',
    'IHM_MTD': 'Industrial Hygiene Monitoring (MTD)',
    'SnRaised': 'Safety Notification Raised',
    'SnClosed': 'Safety Notification Closed',
    'SnOpen': 'Safety Notification Open',
    'SnOpen90days': 'Safety Notification Open > 90 Days',
    'SafetyWalkthrough': 'Safety Walkthrough by Senior leadership Team' // Added back
  };
  isLoading: boolean = false;

  filters: MisFilter = {
    year: new Date().getFullYear(),
    plantId: null, // Changed from plantIds array to single plantId
    clusterId: null,
    opcoId: null,
    plantTypeId: null,
  };
  isFilterModalOpen: boolean = false;

  availablePlants: Plant[] = [];
  availableClusters: Cluster[] = [];
  availableCompanies: Company[] = [];
  availablePlantTypes: PlantType[] = [];

  // User role properties
  currentUserRole: string = '';
  loggedInAdminId: number | null = null;
  loggedInPlantIds: number[] = [];
  componentRoles = {
    SUPER_ADMIN: 'super_admin',
    PLANT_ADMIN: 'plant_admin'
  };
  isPlantAdmin: boolean = false;

  // Ng-select specific properties
  isAllPlantsSelected = false;

  constructor(
    private digisafeService: DigisafeService,
    private plantManagementService: PlantManagementService,
    private clusterService: ClusterService,
    private opcoService: OpcoService,
    private plantTypeService: PlantTypeService
  ) {}

  ngOnInit(): void {
    // Initialize with default months to ensure they're displayed immediately
    this.months = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];

    // Initialize the transformed data structure with empty values
    this.initializeTransformedData();

    // Check user role
    this.checkUserRole();

    // Load the actual data
    this.loadInitialData();
  }

  // Ng-select specific methods
  onPlantFilterChange(): void {
    console.log('Plant filter changed:', this.filters.plantId);
    this.updateSelectAllPlantsState();
  }

  toggleSelectAllPlants(event: Event): void {
    const checkbox = event.target as HTMLInputElement;
    if (checkbox.checked) {
      this.filters.plantId = this.availablePlants.map(plant => plant.id);
      this.isAllPlantsSelected = true;
    } else {
      this.filters.plantId = null;
      this.isAllPlantsSelected = false;
    }
  }

  updateSelectAllPlantsState(): void {
    if (!this.filters.plantId || this.filters.plantId.length === 0) {
      this.isAllPlantsSelected = false;
    } else if (this.filters.plantId.length === this.availablePlants.length) {
      this.isAllPlantsSelected = true;
    } else {
      this.isAllPlantsSelected = false;
    }
  }

  // Check if the current user is a plant admin and get their assigned plants
  checkUserRole(): void {
    try {
      const userString = localStorage.getItem('user');
      if (userString) {
        const currentUser = JSON.parse(userString);
        console.log('Current User:', currentUser);

        this.loggedInAdminId = currentUser?.id ?? null;
        this.loggedInPlantIds = (Array.isArray(currentUser?.plantIds) && currentUser.plantIds.length > 0)
          ? currentUser.plantIds.map((id: any) => Number(id)).filter((id: number) => !isNaN(id))
          : [];

        const roleId = currentUser?.adminsRoleId;
        if (roleId === 1) {
          this.currentUserRole = this.componentRoles.SUPER_ADMIN;
          this.isPlantAdmin = false;
        } else if (roleId === 2) {
          this.currentUserRole = this.componentRoles.PLANT_ADMIN;
          this.isPlantAdmin = true;
          if (this.loggedInPlantIds.length === 0) {
            console.error('Plant Admin has no assigned plants!');
            this.toast?.showErrorToast('User configuration error: Plant Admin has no assigned plants.');
          }
        } else {
          this.currentUserRole = '';
          this.isPlantAdmin = false;
        }

        console.log('User Role:', this.currentUserRole);
        console.log('Is Plant Admin:', this.isPlantAdmin);
        console.log('Assigned Plant IDs:', this.loggedInPlantIds);
      }
    } catch (error) {
      console.error('Error checking user role:', error);
    }
  }

  // Initialize the transformed data structure with default months
  initializeTransformedData(): void {
    Object.keys(this.metricLabels).forEach(metric => {
      this.transformedData[metric] = {
        label: this.metricLabels[metric],
        values: {}
      };

      // Initialize with empty values for all months
      this.months.forEach(month => {
        this.transformedData[metric].values[month] = '';
      });
    });
  }

  async loadInitialData(): Promise<void> {
    console.log('Loading initial data for MIS dashboard...');
    try {
      if (this.isPlantAdmin) {
        // For plant admin, only load plants assigned to them
        console.log('Loading data for Plant Admin...');
        await this.getPlants();
      } else {
        // For super admin, load all filter data
        console.log('Loading data for Super Admin...');
        await Promise.all([
          this.getClusters(),
          this.getCompanies(),
          this.getPlantTypes()
        ]);

        // Then load plants (which might depend on clusters)
        await this.getPlants();
      }

      // Finally load the main MIS data
      await this.loadMisData();

      console.log('Initial data loading complete');
    } catch (error) {
      console.error('Error loading initial data:', error);
      this.toast?.showErrorToast('Failed to load some filter data. Please try refreshing the page.');
    }
  }

  async loadMisData(): Promise<void> {
    if (this.isLoading) return;
    this.isLoading = true;
    this.misData = []; // Clear previous data

    // Don't clear transformedData and months on initial load
    // This ensures months are always displayed even if API call is slow
    // We'll update the values when data is received

    const filterQueryParams: any = {};

    // Construct query parameters based on the filters object
    if (this.filters.year !== null) {
      filterQueryParams.year = this.filters.year;
      console.log("Filtering by Year:", this.filters.year);
    }

    if (this.filters.plantId !== null && this.filters.plantId.length > 0) {
      filterQueryParams.plantIds = JSON.stringify(this.filters.plantId);
      console.log("Filtering by Plant IDs:", filterQueryParams.plantIds);
    }

    if (this.filters.clusterId !== null && this.filters.clusterId !== undefined) {
      filterQueryParams.clusterId = this.filters.clusterId;
      console.log("Filtering by Cluster ID:", this.filters.clusterId);
    }

    if (this.filters.opcoId !== null && this.filters.opcoId !== undefined) {
      filterQueryParams.opcoId = this.filters.opcoId;
      console.log("Filtering by OpCo ID:", this.filters.opcoId);
    }

    if (this.filters.plantTypeId !== null && this.filters.plantTypeId !== undefined) {
      filterQueryParams.plantTypeId = this.filters.plantTypeId;
      console.log("Filtering by Plant Type ID:", this.filters.plantTypeId);
    }

    // MIS dashboard does not have pagination, but include limit and sort if needed by API
    filterQueryParams.limit = 1000; // Fetch all data for the selected filters
    filterQueryParams.sort = 'month,ASC'; // Example sort

    try {
      console.log("MIS API Request Query Params:", filterQueryParams);

      // Fetch data using the DigisafeService
      const response = await this.digisafeService.getMisCountData({ params: filterQueryParams });
      console.log("API Response:", response);

      // Handle different response structures
      if (response && typeof response === 'object') {
        if (Array.isArray(response)) {
          // If response is directly an array
          this.misData = response;
        } else if (response.data && Array.isArray(response.data)) {
          // If response has a data property that is an array
          this.misData = response.data;
        } else if (response.responseCode === 200 && response.data && Array.isArray(response.data)) {
          // If response has a responseCode and data property
          this.misData = response.data;
        } else {
          // Try to find an array in the response
          const possibleDataArrays = Object.values(response).filter(val => Array.isArray(val));
          if (possibleDataArrays.length > 0) {
            // Use the first array found
            this.misData = possibleDataArrays[0] as MisMonthlyData[];
          } else {
            this.misData = [];
          }
        }
      } else {
        this.misData = [];
      }

      console.log("Processed MIS Data:", this.misData);

      // If no data is returned or no months are found, create sample data
      if (!this.misData || this.misData.length === 0) {
        console.log("No data returned from API, creating sample data");
        this.createSampleData();
      }

      // Transform data for the new table structure
      this.transformDataForTable();

    } catch (error: any) {
      console.error("Error fetching MIS data:", error);
      this.misData = [];

      // Don't clear transformedData or months on error
      // This ensures the table structure remains intact

      // Create sample data for testing in case of error
      console.log("Error occurred, creating sample data");
      this.createSampleData();
      this.transformDataForTable();

      // Assuming a toast service is available
      // this.toast?.showErrorToast(error?.response?.data?.message || 'Failed to load MIS data.');
    } finally {
      this.isLoading = false;
      // Assuming ChangeDetectorRef is injected and needed
      // this.cdr.detectChanges();
    }
  }

  // Create sample data for testing when API returns no data
  createSampleData(): void {
    const sampleMonths = [1, 2, 3, 4, 5, 6];

    // Create regular monthly data
    const regularMonthData = sampleMonths.map(month => {
      const item: any = { month };

      // Add sample values for each metric in the specified order
      this.getMetrics().forEach(metric => {
        // Generate numeric sample data for better testing
        if (['AfpCurrent', 'AfpBest'].includes(metric)) {
          item[metric] = `${month * 30} days`;
        } else {
          item[metric] = `${Math.floor(Math.random() * 100)}`;
        }
      });

      return item as MisMonthlyData;
    });

    // Create a total entry (month 13)
    const totalItem: any = { month: 13 };

    // Calculate totals for each metric from the regular month data
    this.getMetrics().forEach(metric => {
      // Special handling for AfpCurrent and AfpBest which are not numeric
      if (['AfpCurrent', 'AfpBest'].includes(metric)) {
        totalItem[metric] = '0'; // Not applicable for totals
      } else {
        let total = 0;
        regularMonthData.forEach(monthData => {
          const value = monthData[metric as keyof MisMonthlyData];
          if (value && !isNaN(Number(value))) {
            total += Number(value);
          }
        });
        totalItem[metric] = total.toString();
      }
    });

    // Combine regular month data with the total
    this.misData = [...regularMonthData, totalItem as MisMonthlyData];

    console.log("Created sample data:", this.misData);
  }

  openFilterModal(): void {
    this.isFilterModalOpen = true;
  }

  closeFilterModal(): void {
    this.isFilterModalOpen = false;
  }

  async getPlants(): Promise<void> {
    console.log('Fetching plants...');
    const plantParams: any = { sort: 'name,ASC', filter: ['enabled||eq||true'], limit: 1000 };

    // For plant admin, we'll filter the results after API call
    // But for super admin with cluster filter, add it to the API params
    if (!this.isPlantAdmin && this.filters.clusterId !== null && this.filters.clusterId !== undefined) {
      plantParams.filter.push(`clusterId||eq||${this.filters.clusterId}`);
      console.log("Filtering plants by Cluster ID:", this.filters.clusterId);
    }

    let allEnabledPlants: Plant[] = [];
    try {
      const axiosConfig = createAxiosConfig(plantParams);
      console.log("Plant API Request Params:", JSON.stringify(plantParams, null, 2));

      const response = await this.plantManagementService.getPlants(axiosConfig);
      console.log("Plant API Response:", response);

      // Handle different response structures
      if (response && typeof response === 'object') {
        if (Array.isArray(response)) {
          allEnabledPlants = response;
        } else if (response.data && Array.isArray(response.data)) {
          allEnabledPlants = response.data;
        } else {
          console.error("Unexpected response format for plants:", response);
          allEnabledPlants = [];
        }
      } else {
        console.error("Invalid response for plants:", response);
        allEnabledPlants = [];
      }

      console.log("Processed plants data:", allEnabledPlants);

      // Filter plants based on user role
      if (this.isPlantAdmin && this.loggedInPlantIds.length > 0) {
        // For plant admin, only show assigned plants
        allEnabledPlants = allEnabledPlants.filter(plant =>
          this.loggedInPlantIds.includes(plant.id)
        );

        console.log("Filtered plants for Plant Admin:", allEnabledPlants);

        // If a cluster filter is applied, further filter the plants
        if (this.filters.clusterId !== null && this.filters.clusterId !== undefined) {
          allEnabledPlants = allEnabledPlants.filter(plant =>
            plant.clusterId === this.filters.clusterId
          );
          console.log("Further filtered by cluster:", allEnabledPlants);
        }

        // If no plants are available after filtering, show a message
        if (allEnabledPlants.length === 0) {
          this.toast?.showErrorToast('No plants available for the selected filters.');
        }
      }
    } catch (error) {
      console.error("Error fetching plants:", error);
      allEnabledPlants = [];
      this.toast?.showErrorToast('Failed to load plant list.');
    }

    this.availablePlants = allEnabledPlants;
    console.log(`Loaded ${this.availablePlants.length} plants.`);

    // For plant admin with only one plant, auto-select it
    if (this.isPlantAdmin && this.availablePlants.length === 1) {
      this.filters.plantId = [this.availablePlants[0].id];
      console.log("Auto-selected the only available plant:", this.filters.plantId);
      this.isAllPlantsSelected = true; // If only one plant and auto-selected, mark as all selected
    } else {
      // Otherwise reset plant selection
      this.filters.plantId = null;
      this.isAllPlantsSelected = false;
    }
    this.updateSelectAllPlantsState(); // Update state after loading plants
  }

  // Method to handle cluster filter change
  async onClusterChange(clusterId: number | null): Promise<void> {
    this.filters.clusterId = clusterId;
    console.log("Cluster selection changed to:", clusterId);

    try {
      // Show loading indicator or message if needed
      // this.isClusterLoading = true;

      // Fetch plants based on the selected cluster
      await this.getPlants();

      // Reset selected plant when cluster changes
      this.filters.plantId = null;
      this.isAllPlantsSelected = false; // Reset select all checkbox for plants

      console.log("Plants updated based on cluster selection");
    } catch (error) {
      console.error("Error updating plants for selected cluster:", error);
      this.toast?.showErrorToast('Failed to update plants for the selected cluster.');
    } finally {
      // Hide loading indicator if needed
      // this.isClusterLoading = false;
    }
  }

  async getClusters(): Promise<void> {
    console.log('Fetching clusters (Placeholder)...');
    // Replace with actual API call using ClusterService
    try {
      // const clusterParams = { sort:'title,ASC', filter:['enabled||eq||true'], limit: 1000 };
      // const axiosConfig = createAxiosConfig(clusterParams);
      // const response = await this.clusterService.getClusters(axiosConfig);
      // this.availableClusters = response?.data ?? response ?? [];
      const clusterParams = { sort:'title,ASC', filter:['enabled||eq||true'], limit: 1000 };
      const axiosConfig = createAxiosConfig(clusterParams);
      const response = await this.clusterService.getCluster(axiosConfig);
      this.availableClusters = response?.data ?? response ?? [];
      console.log('Clusters loaded:', this.availableClusters);
    } catch (error) {
      console.error("Error fetching clusters:", error);
      this.availableClusters = [];
      // this.toast?.showErrorToast('Failed to load cluster list.');
    }
  }

  async getCompanies(): Promise<void> {
    console.log('Fetching companies (OpCos) (Placeholder)...');
    // Replace with actual API call using OpcoService
    try {
      // const companyParams = { sort:'title,ASC', filter:['enabled||eq||true'], limit: 1000 };
      // const axiosConfig = createAxiosConfig(companyParams);
      // const response = await this.opcoService.getOpcos(axiosConfig); // Assuming getOpcos method
      // this.availableCompanies = response?.data ?? response ?? [];
      const companyParams = { sort:'title,ASC', filter:['enabled||eq||true'], limit: 1000 };
      const axiosConfig = createAxiosConfig(companyParams);
      const response = await this.opcoService.getOpco(axiosConfig); // Assuming getOpcos method
      this.availableCompanies = response?.data ?? response ?? [];
      console.log('Companies loaded:', this.availableCompanies);
    } catch (error) {
      console.error("Error fetching companies:", error);
      this.availableCompanies = [];
      // this.toast?.showErrorToast('Failed to load company list.');
    }
  }

  async getPlantTypes(): Promise<void> {
    console.log('Fetching plant types (Placeholder)...');
    // Replace with actual API call using PlantTypeService
    try {
      // const plantTypeParams = { sort:'title,ASC', filter:['enabled||eq||true'], limit: 1000 };
      // const axiosConfig = createAxiosConfig(plantTypeParams);
      // const response = await this.plantTypeService.getPlantTypes(axiosConfig);
      // this.availablePlantTypes = response?.data ?? response ?? [];
      const plantTypeParams = { sort:'title,ASC', filter:['enabled||eq||true'], limit: 1000 };
      const axiosConfig = createAxiosConfig(plantTypeParams);
      const response = await this.plantTypeService.getPlantType(axiosConfig);
      this.availablePlantTypes = response?.data ?? response ?? [];
      console.log('Plant types loaded:', this.availablePlantTypes);
    } catch (error) {
      console.error("Error fetching plant types:", error);
      this.availablePlantTypes = [];
      // this.toast?.showErrorToast('Failed to load plant type list.');
    }
  }



  // Triggered when filters are confirmed
  applyFilters(): void {
    console.log("Applying filters:", this.filters);
    this.loadMisData();
    this.closeFilterModal(); // Close modal after applying
  }

  // Reset Filters
  resetFilters(): void {
    this.filters = {
      year: new Date().getFullYear(),
      plantId: null, // Changed from plantIds array to single plantId
      clusterId: null,
      opcoId: null,
      plantTypeId: null,
    };
    this.isAllPlantsSelected = false; // Reset select all checkbox
    this.loadMisData();
    this.closeFilterModal(); // Close modal after reset
  }

  /**
   * Converts a month number (1-12) to its full name and formats it with the year.
   * Special case: if monthNumber is -1, returns "Total" as this is the total column.
   * @param monthNumber The month number (1 for January, 12 for December) or -1 for Total.
   * @param year The year.
   * @returns The formatted string "YYYY-Month Name" or "Total".
   */
  getMonthName(monthNumber: number, year: number | null): string {
    // Special case for the total column
    if (monthNumber === -1) {
      return 'Total';
    }

    if (year === null) {
      // Handle case where year is not available, maybe return just month name or a placeholder
      const date = new Date(2000, monthNumber - 1, 1);
      return date.toLocaleString('en-US', { month: 'long' });
    }
    const date = new Date(year, monthNumber - 1, 1);
    const monthName = date.toLocaleString('en-US', { month: 'long' });
    return `${year}-${monthName}`;
  }

  // Transform data for the new table structure (months as columns, metrics as rows)
  transformDataForTable(): void {
    console.log('Starting data transformation with misData:', this.misData);

    // Store current months to preserve them if needed
    const currentMonths = [...this.months];

    // Only update months if we have valid data
    if (this.misData && Array.isArray(this.misData) && this.misData.length > 0) {
      try {
        // Check if month property exists in the data
        const hasMonthProperty = this.misData.some(item => 'month' in item);

        if (hasMonthProperty) {
          // Extract months from data, but filter out month 13 (total) for regular months display
          const dataMonths = [...new Set(this.misData.map(item => item.month))]
            .filter(month => month !== 13) // Filter out month 13 (total) from regular months
            .sort((a, b) => a - b);

          if (dataMonths.length > 0) {
            // Only update months if we found some in the data
            this.months = dataMonths;
            console.log('Months extracted from data:', this.months);
          } else {
            // Keep current months if no new ones found
            console.log('No months found in data, keeping current months:', currentMonths);
          }
        } else {
          // Keep current months if no month property
          console.log('No month property found in data items, keeping current months');
        }
      } catch (error) {
        console.error('Error extracting months:', error);
        // Keep current months in case of error
        this.months = currentMonths;
      }
    }

    // If months array is somehow empty, use default months
    if (this.months.length === 0) {
      this.months = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
      console.log('Using default months after all checks:', this.months);
    }

    // Add a special 'total' identifier to the months array
    // Using -1 as a special value to represent the total column
    if (!this.months.includes(-1)) {
      this.months.push(-1);
    }

    // Initialize transformed data structure with all metrics
    Object.keys(this.metricLabels).forEach(metric => {
      this.transformedData[metric] = {
        label: this.metricLabels[metric],
        values: {}
      };

      // Initialize with empty values for all months
      this.months.forEach(month => {
        this.transformedData[metric].values[month] = '';
      });
    });

    // Fill in the values from misData if it's valid
    if (Array.isArray(this.misData) && this.misData.length > 0) {
      this.misData.forEach(item => {
        if (item && typeof item === 'object' && 'month' in item) {
          // Handle month 13 (total) data separately
          if (item.month === 13) {
            // Store the total values from month 13 in the special -1 index
            Object.keys(this.metricLabels).forEach(metric => {
              if (metric in item) {
                this.transformedData[metric].values[-1] = item[metric as keyof MisMonthlyData];
              }
              // Special case for LSI - check LeakSpillIncident if LSI is not present
              else if (metric === 'LSI' && 'LeakSpillIncident' in item) {
                this.transformedData[metric].values[-1] = item['LeakSpillIncident'];
              }
              // Special case for SafetyWalkthrough - ensure it's displayed if present
              else if (metric === 'SafetyWalkthrough' && 'SafetyWalkthrough' in item) {
                this.transformedData[metric].values[-1] = item['SafetyWalkthrough'];
              }
            });
          } else {
            // Handle regular months (1-12)
            Object.keys(this.metricLabels).forEach(metric => {
              if (metric in item) {
                this.transformedData[metric].values[item.month] = item[metric as keyof MisMonthlyData];
              }
              // Special case for LSI - check LeakSpillIncident if LSI is not present
              else if (metric === 'LSI' && 'LeakSpillIncident' in item) {
                this.transformedData[metric].values[item.month] = item['LeakSpillIncident'];
              }
              // Special case for SafetyWalkthrough - ensure it's displayed if present
              else if (metric === 'SafetyWalkthrough' && 'SafetyWalkthrough' in item) {
                this.transformedData[metric].values[item.month] = item['SafetyWalkthrough'];
              }
            });
          }
        }
      });
    }

    // If no total data was found in the API response (month 13), calculate totals
    const hasTotalData = this.misData.some(item => item.month === 13);
    if (!hasTotalData) {
      console.log('No total data (month 13) found in API response, calculating totals manually');

      // Calculate totals for each metric
      Object.keys(this.metricLabels).forEach(metric => {
        let total = 0;
        let hasValidValues = false;

        // Get all regular months (excluding the total column)
        const regularMonths = this.months.filter(month => month !== -1);

        regularMonths.forEach(month => {
          const value = this.transformedData[metric].values[month];
          if (value !== '' && !isNaN(Number(value))) {
            total += Number(value);
            hasValidValues = true;
          }
        });

        // Set the total value for this metric only if it wasn't already set from API data
        if (this.transformedData[metric].values[-1] === '') {
          this.transformedData[metric].values[-1] = hasValidValues ? total.toString() : '';
        }
      });
    }

    console.log('Transformed data structure:', this.transformedData);
    console.log('Final months array:', this.months);
  }

  // Get metrics as an array for template iteration in the specified order
  getMetrics(): string[] {
    // Return metrics in the exact order specified in the requirements
    const metrics = [
      'AfpCurrent',        // 1 - Accident Free Period (Current)
      'AfpBest',           // 2 - Accident Free Period (Best)
      'ManHoursWorked',    // 3 - Man Hours Worked
      'KmCovered',         // 4 - Kilometres Covered
      'OI',                // 5 - Occupational Illness (OI)
      'FAC',               // 6 - First Aid Case (FAC)
      'MTC',               // 7 - Medical Treatment Case (MTC)
      'RLTI',              // 8 - Reportable Lost Time Injury (R-LTI)
      'Fatal',             // 9 - Fatal
      'DangerOccurence',   // 10 - Dangerous Occurrence (DO)
      'FireIncident',      // 11 - Fire Incident
      'VRA',               // 12 - Vehicle related Accident
      'propertyDamage',    // 13 - Property Damage Incident Nos. (Except Vehicle & Fire Incidents)
      'LSI',               // 14 - Leak-Spill Incident
      'AccidentCost',      // 15 - Accident Cost INR (Divided by 1000)
      'MDL',               // 16 - Man Days Lost (MDL)
      'NearMissIncident',  // 17 - Near miss Incident (including High Potential Near Miss Case)
      'SafetyInduction',   // 18 - Safety Induction (Participants)
      'TBTBatches',        // 19 - Tool Box Talk (No of batches.)
      'TBTno',             // 20 - TBT- Participant Nos.
      'STP_Plan',          // 21 - Safety Training Program Plan (Batches)
      'STP_MTD',           // 22 - Safety Training Program - MTD (Batches)
      'ET_Plan',           // 23 - Employee Trained - Plan (Nos)
      'ET_MTD',            // 24 - Employee Trained - MTD (Nos)
      'TrainingManHours',  // 25 - Training Man-Hours (MTD)
      'SCMPlan',           // 26 - Safety Committee Meetings (Plan)
      'SCM_MTD',           // 27 - Safety Committee Meetings (MTD)
      'PWAPlan',           // 28 - Permit To Work Audit (Plan)
      'PWA_MTD',           // 29 - Permit To Work Audit (MTD)
      'SA_MTD',            // 30 - Self Assessment (MTD)
      'SI_Plan',           // 32 - Safety-Item / Equipment Inspection (Plan)
      'SI_MTD',            // 33 - Safety-Item / Equipment Inspection (MTD)
      'SPC_Plan',          // 34 - Safety Promotional Campaign (Plan)
      'SPC_MTD',           // 35 - Safety Promotional Campaign (MTD)
      'EMD_Plan',          // 36 - Emergency Mock Drill (Plan)
      'EMD_MTD',           // 37 - Emergency Mock Drill (MTD)
      'IHM_Plan',          // 38 - Industrial Hygiene Monitoring (Plan)
      'IHM_MTD',           // 39 - Industrial Hygiene Monitoring (MTD)
      'SnRaised',          // 40 - Safety Notification Raised
      'SnClosed',          // 41 - Safety Notification Closed
      'SnOpen',            // 42 - Safety Notification Open
      'SnOpen90days',      // 43 - Safety Notification Open > 90 Days
      'SafetyWalkthrough'  // 44 - Safety Walkthrough by Senior leadership Team
    ];

    // Filter out metrics that don't exist in metricLabels
    return metrics.filter(metric => metric in this.metricLabels);
  }

  // Track by functions for ngFor to improve performance
  trackByMonth(_index: number, month: number): number {
    return month;
  }

  trackByMetric(_index: number, metric: string): string {
    return metric;
  }

  // Format date for display
  formatDate(date: string | Date | undefined, format: string): string {
    if (!date) return '0';

    const d = new Date(date);
    if (isNaN(d.getTime())) return '0';

    if (format === 'MMM YYYY') {
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      return `${months[d.getMonth()]} ${d.getFullYear()}`;
    }

    return d.toLocaleDateString();
  }

  // Download Excel file
  async downloadExcel(type: 'current' | 'all'): Promise<void> {
    if (this.isDownloadingExcel) {
      return;
    }

    this.isDownloadingExcel = true;
    this.downloadType = type;
    this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current' : 'all filtered'} MIS data...`);

    try {
      // For MIS dashboard, we already have all the data loaded
      // No need to fetch additional data like in manage-digisafe

      if (!this.misData || this.misData.length === 0) {
        this.toast?.showErrorToast(`No MIS data available to download.`);
        return;
      }

      // Transform data for Excel export with months as columns and metrics as rows
      // First, extract all unique months from the data, excluding month 13 (total)
      const uniqueMonths = [...new Set(this.misData.map(item => item.month))]
        .filter(month => month !== 13) // Filter out month 13 (total) from regular months
        .sort((a, b) => a - b);

      // Create a structure similar to the table display
      const excelData: any[] = [];

      // Add header row with month names
      const headerRow: any = { 'Metric': 'Metric' };
      uniqueMonths.forEach(month => {
        headerRow[month.toString()] = this.getMonthName(month, this.filters.year);
      });
      // Add Total column to header
      headerRow['total'] = 'Total';
      excelData.push(headerRow);

      // Add data rows for each metric in the specified order
      this.getMetrics().forEach(metric => {
        const metricRow: any = { 'Metric': this.metricLabels[metric] };
        let total = 0;
        let hasValidValues = false;

        // Add values for each month
        uniqueMonths.forEach(month => {
          // Find the data for this month
          const monthData = this.misData.find(item => item.month === month);

          // Handle special case for LSI (check both LSI and LeakSpillIncident)
          let value = '0';
          if (monthData) {
            if (metric === 'LSI' && monthData['LSI'] === undefined && monthData['LeakSpillIncident'] !== undefined) {
              // If LSI is not present but LeakSpillIncident is, use that value
              value = monthData['LeakSpillIncident']?.toString() || '0';
            } else {
              value = (monthData[metric as keyof MisMonthlyData])?.toString() || '0';
            }
          }

          metricRow[month.toString()] = value;

          // Calculate total if value is numeric (we'll use this if no month 13 data exists)
          if (value !== 'N/A' && !isNaN(Number(value))) {
            total += Number(value);
            hasValidValues = true;
          }
        });

        // Try to find the total from month 13 data
        const totalData = this.misData.find(item => item.month === 13);
        if (totalData) {
          // Handle special case for LSI in total data
          if (metric === 'LSI' && totalData['LSI'] === undefined && totalData['LeakSpillIncident'] !== undefined) {
            metricRow['total'] = totalData['LeakSpillIncident']?.toString() || '0';
          }
          // Handle special case for SafetyWalkthrough in total data
          else if (metric === 'SafetyWalkthrough' && totalData['SafetyWalkthrough'] !== undefined) {
            metricRow['total'] = totalData['SafetyWalkthrough']?.toString() || '0';
          }
          // Regular case - use the metric value from month 13
          else if (metric in totalData) {
            metricRow['total'] = totalData[metric as keyof MisMonthlyData]?.toString() || '0';
          }
          // Fallback to calculated total if the metric is not in month 13 data
          else {
            metricRow['total'] = hasValidValues ? total.toString() : '0';
          }
        } else {
          // Fall back to calculated total if no month 13 data exists
          metricRow['total'] = hasValidValues ? total.toString() : '0';
        }

        excelData.push(metricRow);
      });

      // Create worksheet with specific column order to ensure Metric is first
      // Define the column order with Metric first, then months, then total
      const columnOrder = ['Metric', ...uniqueMonths.map(month => month.toString()), 'total'];

      // Create worksheet with the specified column order
      const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(excelData, { header: columnOrder });
      const wb: XLSX.WorkBook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'MIS Dashboard');

      // Auto-size columns (optional enhancement)
      const colWidths = [];
      for (let i = 0; i < Object.keys(excelData[0] || {}).length; i++) {
        colWidths.push({ wch: 20 }); // Set default width
      }
      ws['!cols'] = colWidths;

      // Generate filename with date
      const dateStr = new Date().toISOString().slice(0, 10);
      const yearStr = this.filters.year ? `_${this.filters.year}` : '';
      const plantStr = this.filters.plantId ? `_Plant${this.filters.plantId}` : '';
      const fileName = `MIS_Dashboard${yearStr}${plantStr}_${dateStr}.xlsx`;

      // Write file and download
      XLSX.writeFile(wb, fileName);
      this.toast?.showSuccessToast(`Excel file download started.`);
    } catch (error) {
      console.error(`Error generating Excel file:`, error);
      this.toast?.showErrorToast(`An error occurred while generating the Excel file.`);
    } finally {
      this.isDownloadingExcel = false;
      this.downloadType = null;
    }
  }
}
