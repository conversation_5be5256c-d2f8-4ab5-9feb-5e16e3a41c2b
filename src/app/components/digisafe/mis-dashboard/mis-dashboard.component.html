<app-toast-message></app-toast-message>
<div class="card custom-card">
    <div class="card-header">
      <div class="row align-items-center">
        <div class="col">
          <h6 class="mb-0">MIS Dashboard</h6>
        </div>
        <div class="col text-end d-flex align-items-center justify-content-end">
          <!-- Download Dropdown -->
          <div ngbDropdown class="d-inline-block me-2">
            <button type="button" class="btn btn-sm adani-btn dropdown-toggle"
              id="downloadMisExcelDropdown" ngbDropdownToggle
              [disabled]="isDownloadingExcel || isLoading">
              <span *ngIf="!isDownloadingExcel">
                <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
              </span>
              <span *ngIf="isDownloadingExcel">
                <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                Downloading...
              </span>
            </button>
            <ul ngbDropdownMenu aria-labelledby="downloadMisExcelDropdown">
              <li>
                <button type="button" ngbDropdownItem (click)="downloadExcel('current')"
                  [disabled]="isDownloadingExcel || isLoading || misData.length === 0">
                  <i class="bi bi-download me-1"></i> Download Current Data ({{ misData.length }})
                </button>
              </li>
              <li>
                <button type="button" ngbDropdownItem (click)="downloadExcel('all')"
                  [disabled]="isDownloadingExcel || isLoading || misData.length === 0">
                  <i class="bi bi-cloud-download me-1"></i> Download All Data ({{ misData.length }})
                </button>
              </li>
            </ul>
          </div>
          <!-- Filter Button -->
          <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()" alt="Filter" style="width: 35px; cursor: pointer;" title="Filter MIS Data"/>
        </div>
      </div>
    </div>
    <div class="card-body">
      <!-- Hidden debug info - only visible in browser console -->
      <div style="display: none;" id="debug-months">{{ months.length }} months: {{ months.join(', ') }}</div>

      <!-- Force months to be displayed if they're not showing -->
      <div *ngIf="months.length === 0" [style.display]="'none'">
        {{ initializeTransformedData() }}
      </div>

      <div class="table-responsive">
        <table class="table custom-table">
          <thead>
            <tr>
              <th>Metric</th>
              <!-- Months as columns -->
              <th *ngFor="let month of months; trackBy: trackByMonth"
                  [ngClass]="{'total-column': month === -1}">
                {{ getMonthName(month, filters.year) }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngIf="isLoading">
              <td [attr.colspan]="months.length + 1" class="text-center p-4">
                <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                Loading MIS data...
              </td>
            </tr>
            <tr *ngIf="!isLoading && (!misData || misData.length === 0)">
              <td [attr.colspan]="months.length + 1" class="text-center p-4 text-muted">
                No MIS data found matching the current filters.
              </td>
            </tr>
            <!-- Metrics as rows -->
            <tr *ngFor="let metric of getMetrics(); trackBy: trackByMetric">
              <td>{{ transformedData[metric].label }}</td>
              <td *ngFor="let month of months; trackBy: trackByMonth"
                  [ngClass]="{'total-column': month === -1}">
                {{ transformedData[metric].values[month] }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>

<!-- Filter Offcanvas -->
<app-offcanvas [title]="'Filter MIS Data'" *ngIf="isFilterModalOpen" (onClickCross)="closeFilterModal()">
  <div class="filter-container p-3">
    <form #filterForm="ngForm" (ngSubmit)="applyFilters()">
      <div class="row g-3">

        <!-- Year Filter -->
        <div class="col-md-6">
          <label for="filterYear" class="form-label small">Year</label>
          <input type="number" id="filterYear" class="form-control form-control-sm" [(ngModel)]="filters.year" name="yearFilter">
        </div>

        <!-- Cluster Filter - Only visible for Super Admin -->
        <div class="col-md-6" *ngIf="!isPlantAdmin">
          <label for="filterCluster" class="form-label small">Select Cluster</label>
          <select id="filterCluster" class="form-select form-select-sm" [(ngModel)]="filters.clusterId" name="clusterFilter" (change)="onClusterChange(filters.clusterId)">
            <option [ngValue]="null">All Clusters</option>
            <option *ngFor="let cluster of availableClusters" [value]="cluster.id">{{ cluster.title }}</option>
          </select>
        </div>

        <!-- Company (OpCo) Filter - Only visible for Super Admin -->
        <div class="col-md-6" *ngIf="!isPlantAdmin">
          <label for="filterCompany" class="form-label small">Select Company</label>
          <select id="filterCompany" class="form-select form-select-sm" [(ngModel)]="filters.opcoId" name="companyFilter">
            <option [ngValue]="null">All Companies</option>
            <option *ngFor="let company of availableCompanies" [value]="company.id">{{ company.title }}</option>
          </select>
        </div>

        <!-- Plant Type Filter - Only visible for Super Admin -->
        <div class="col-md-6" *ngIf="!isPlantAdmin">
          <label for="filterPlantType" class="form-label small">Select Plant Type</label>
          <select id="filterPlantType" class="form-select form-select-sm" [(ngModel)]="filters.plantTypeId" name="plantTypeFilter">
            <option [ngValue]="null">All Plant Types</option>
            <option *ngFor="let type of availablePlantTypes" [value]="type.id">{{ type.title }}</option>
          </select>
        </div>

        <!-- Plant Filter (Single Select) -->
        <div class="col-12">
          <label for="filterPlants" class="form-label small">Select Plant</label>
          <ng-select
            [items]="availablePlants"
            bindLabel="name"
            bindValue="id"
            [multiple]="true"
            placeholder="Select one or more plants"
            [(ngModel)]="filters.plantId"
            name="plantFilter"
            [closeOnSelect]="false"
            [searchable]="true"
            [clearSearchOnAdd]="true"
            id="filterPlants"
            (change)="onPlantFilterChange()">
            <ng-template ng-header-tmp>
                <div class="form-check mb-1 ms-2">
                    <input class="form-check-input" type="checkbox"
                           id="selectAllPlantsCheckboxMis"
                           [checked]="isAllPlantsSelected"
                           (change)="toggleSelectAllPlants($event)">
                    <label class="form-check-label small" for="selectAllPlantsCheckboxMis">
                        Select All / Deselect All
                    </label>
                </div>
            </ng-template>
          </ng-select>
        </div>

        <!-- Action Buttons -->
        <div class="col-12 mt-4 d-grid gap-2">
          <button type="submit" class="btn adani-btn btn-sm">
            <i class="bi bi-search me-1"></i> Apply Filters
          </button>
          <button type="button" class="btn btn-secondary btn-sm" (click)="resetFilters()">
            <i class="bi bi-arrow-clockwise me-1"></i> Reset Filters
          </button>
        </div>
      </div>
    </form>
  </div>
</app-offcanvas>
